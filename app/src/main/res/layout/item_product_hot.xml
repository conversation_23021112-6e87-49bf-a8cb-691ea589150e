<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="130dp"
    android:layout_height="153dp"
    android:layout_marginEnd="8dp"

    android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="94dp"
                android:background="@drawable/bg_product_card">

                <ImageView
                    android:id="@+id/iv_product_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="22dp"
                    android:layout_marginHorizontal="10dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="产品图片"
                    tools:src="@tools:sample/backgrounds/scenic" />

            </FrameLayout>

            <TextView
                android:id="@+id/tv_sales_rank"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:layout_gravity="top|start"
                android:background="@drawable/bg_top_label"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textColor="@color/white"
                android:textSize="8sp"
                android:elevation="4dp"
                android:translationZ="4dp"
                tools:text="月销量TOP1" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            style="@style/normalText"
            android:textColor="@android:color/black"
            android:textSize="12sp"
            tools:text="笔果AI学习机" />

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="1dp"
            android:textColor="#F44336"
            android:textSize="14sp"
            style="@style/normalText"
            tools:text="¥2500.00" />
</LinearLayout>
